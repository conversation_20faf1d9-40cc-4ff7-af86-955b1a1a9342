#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="$basedir/../../../../../mkdirp@1.0.4/node_modules"
else
  export NODE_PATH="$NODE_PATH:$basedir/../../../../../mkdirp@1.0.4/node_modules"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../../../../mkdirp@1.0.4/node_modules/mkdirp/bin/cmd.js" "$@"
else
  exec node  "$basedir/../../../../../mkdirp@1.0.4/node_modules/mkdirp/bin/cmd.js" "$@"
fi
