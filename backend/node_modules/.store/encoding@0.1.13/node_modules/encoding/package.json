{"name": "encoding", "version": "0.1.13", "description": "Convert encodings, uses iconv-lite", "main": "lib/encoding.js", "scripts": {"test": "nodeunit test"}, "repository": "https://github.com/andris9/encoding.git", "author": "<PERSON><PERSON>", "license": "MIT", "dependencies": {"iconv-lite": "^0.6.2"}, "devDependencies": {"nodeunit": "0.11.3"}, "__npminstall_done": true, "_from": "encoding@0.1.13", "_resolved": "https://registry.npmmirror.com/encoding/-/encoding-0.1.13.tgz"}