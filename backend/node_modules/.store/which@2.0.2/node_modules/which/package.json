{"author": "<PERSON> <<EMAIL>> (http://blog.izs.me)", "name": "which", "description": "Like which(1) unix command. Find the first instance of an executable in the PATH.", "version": "2.0.2", "repository": {"type": "git", "url": "git://github.com/isaacs/node-which.git"}, "main": "which.js", "bin": {"node-which": "./bin/node-which"}, "license": "ISC", "dependencies": {"isexe": "^2.0.0"}, "devDependencies": {"mkdirp": "^0.5.0", "rimraf": "^2.6.2", "tap": "^14.6.9"}, "scripts": {"test": "tap", "preversion": "npm test", "postversion": "npm publish", "prepublish": "npm run changelog", "prechangelog": "bash gen-changelog.sh", "changelog": "git add CHANGELOG.md", "postchangelog": "git commit -m 'update changelog - '${npm_package_version}", "postpublish": "git push origin --follow-tags"}, "files": ["which.js", "bin/node-which"], "tap": {"check-coverage": true}, "engines": {"node": ">= 8"}, "__npminstall_done": true, "_from": "which@2.0.2", "_resolved": "https://registry.npmmirror.com/which/-/which-2.0.2.tgz"}