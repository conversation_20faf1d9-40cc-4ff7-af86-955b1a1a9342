{"name": "minipass-sized", "version": "1.0.3", "description": "A Minipass stream that raises an error if you get a different number of bytes than expected", "author": "<PERSON> <<EMAIL>> (https://izs.me)", "license": "ISC", "scripts": {"test": "tap", "snap": "tap", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --follow-tags"}, "tap": {"check-coverage": true}, "devDependencies": {"tap": "^14.6.4"}, "dependencies": {"minipass": "^3.0.0"}, "main": "index.js", "keywords": ["minipass", "size", "length"], "directories": {"test": "test"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/minipass-sized.git"}, "engines": {"node": ">=8"}, "__npminstall_done": true, "_from": "minipass-sized@1.0.3", "_resolved": "https://registry.npmmirror.com/minipass-sized/-/minipass-sized-1.0.3.tgz"}