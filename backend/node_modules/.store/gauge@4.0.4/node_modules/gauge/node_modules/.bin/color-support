#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="$basedir/../../../../../color-support@1.1.3/node_modules"
else
  export NODE_PATH="$NODE_PATH:$basedir/../../../../../color-support@1.1.3/node_modules"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../../../../color-support@1.1.3/node_modules/color-support/bin.js" "$@"
else
  exec node  "$basedir/../../../../../color-support@1.1.3/node_modules/color-support/bin.js" "$@"
fi
