{"cors": ["2.8.5"], "@types/express": ["4.17.23"], "object-assign": ["4.1.1"], "@types/serve-static": ["1.15.8"], "vary": ["1.1.2"], "@types/body-parser": ["1.19.6"], "@types/qs": ["6.14.0"], "express": ["4.21.2"], "escape-html": ["1.0.3"], "@types/connect": ["3.4.38"], "joi": ["17.13.3"], "@types/send": ["0.17.5"], "@types/http-errors": ["2.0.5"], "@types/express-serve-static-core": ["4.19.6"], "@types/node": ["20.19.7"], "debug": ["2.6.9", "4.4.1"], "cookie": ["0.7.1"], "@types/cors": ["2.8.19"], "content-type": ["1.0.5"], "utils-merge": ["1.0.1"], "encodeurl": ["2.0.0", "1.0.2"], "content-disposition": ["0.5.4"], "etag": ["1.8.1"], "safe-buffer": ["5.2.1"], "finalhandler": ["1.3.1"], "array-flatten": ["1.1.1"], "depd": ["2.0.0"], "http-errors": ["2.0.0"], "type-is": ["1.6.18"], "parseurl": ["1.3.3"], "on-finished": ["2.4.1"], "serve-static": ["1.16.2"], "cookie-signature": ["1.0.6"], "setprototypeof": ["1.2.0"], "fresh": ["0.5.2"], "methods": ["1.1.2"], "range-parser": ["1.2.1"], "proxy-addr": ["2.0.7"], "send": ["0.19.0"], "@types/mime": ["1.3.5"], "@types/range-parser": ["1.2.7"], "qs": ["6.13.0"], "@hapi/topo": ["5.1.0"], "statuses": ["2.0.1"], "merge-descriptors": ["1.0.3"], "path-to-regexp": ["0.1.12"], "ms": ["2.0.0", "2.1.3"], "accepts": ["1.3.8"], "@sideway/pinpoint": ["2.0.0"], "body-parser": ["1.20.3"], "@sideway/formula": ["3.0.1"], "@sideway/address": ["4.1.5"], "@hapi/hoek": ["9.3.0"], "undici-types": ["6.21.0"], "ts-node-dev": ["2.0.0"], "inherits": ["2.0.4"], "toidentifier": ["1.0.1"], "media-typer": ["0.3.0"], "mime-types": ["2.1.35"], "forwarded": ["0.2.0"], "unpipe": ["1.0.0"], "side-channel": ["1.1.0"], "ee-first": ["1.1.1"], "mime": ["1.6.0"], "ipaddr.js": ["1.9.1"], "raw-body": ["2.5.2"], "tree-kill": ["1.2.2"], "source-map-support": ["0.5.21"], "chokidar": ["3.6.0"], "rimraf": ["2.7.1", "3.0.2"], "bytes": ["3.1.2"], "negotiator": ["0.6.3", "0.6.4"], "mime-db": ["1.52.0"], "destroy": ["1.2.0"], "side-channel-weakmap": ["1.0.2"], "side-channel-map": ["1.0.1"], "tsconfig": ["7.0.0"], "iconv-lite": ["0.4.24", "0.6.3"], "side-channel-list": ["1.0.0"], "es-errors": ["1.3.0"], "sqlite": ["4.2.1"], "minimist": ["1.2.8"], "object-inspect": ["1.13.4"], "is-binary-path": ["2.1.0"], "is-glob": ["4.0.3"], "anymatch": ["3.1.3"], "normalize-path": ["3.0.0"], "glob-parent": ["5.1.2"], "readdirp": ["3.6.0"], "fsevents": ["2.3.3"], "mkdirp": ["1.0.4"], "buffer-from": ["1.1.2"], "braces": ["3.0.3"], "strip-json-comments": ["2.0.1"], "strip-bom": ["3.0.0"], "safer-buffer": ["2.1.2"], "call-bound": ["1.0.4"], "glob": ["7.2.3"], "get-intrinsic": ["1.3.0"], "@types/strip-json-comments": ["0.0.30"], "dynamic-dedupe": ["0.3.0"], "is-extglob": ["2.1.1"], "source-map": ["0.6.1"], "binary-extensions": ["2.3.0"], "fill-range": ["7.1.1"], "minimatch": ["3.1.2"], "path-is-absolute": ["1.0.1"], "picomatch": ["2.3.1"], "fs.realpath": ["1.0.0"], "@types/strip-bom": ["3.0.0"], "once": ["1.4.0"], "inflight": ["1.0.6"], "es-define-property": ["1.0.1"], "es-object-atoms": ["1.1.1"], "gopd": ["1.2.0"], "get-proto": ["1.0.1"], "call-bind-apply-helpers": ["1.0.2"], "has-symbols": ["1.1.0"], "function-bind": ["1.1.2"], "hasown": ["2.0.2"], "brace-expansion": ["1.1.12"], "resolve": ["1.22.10"], "xtend": ["4.0.2"], "math-intrinsics": ["1.1.0"], "ts-node": ["10.9.2"], "to-regex-range": ["5.0.1"], "wrappy": ["1.0.2"], "balanced-match": ["1.0.2"], "path-parse": ["1.0.7"], "dunder-proto": ["1.0.1"], "concat-map": ["0.0.1"], "supports-preserve-symlinks-flag": ["1.0.0"], "create-require": ["1.1.1"], "arg": ["4.1.3"], "yn": ["3.1.1"], "make-error": ["1.3.6"], "is-core-module": ["2.16.1"], "@tsconfig/node12": ["1.0.11"], "is-number": ["7.0.0"], "v8-compile-cache-lib": ["3.0.1"], "@tsconfig/node16": ["1.0.4"], "@tsconfig/node14": ["1.0.3"], "@tsconfig/node10": ["1.0.11"], "acorn-walk": ["8.3.4"], "@cspotcode/source-map-support": ["0.8.1"], "acorn": ["8.15.0"], "diff": ["4.0.2"], "sqlite3": ["5.1.7"], "bindings": ["1.5.0"], "@jridgewell/trace-mapping": ["0.3.9"], "@jridgewell/sourcemap-codec": ["1.5.4"], "file-uri-to-path": ["1.0.0"], "@jridgewell/resolve-uri": ["3.1.2"], "node-addon-api": ["7.1.1"], "prebuild-install": ["7.1.3"], "tunnel-agent": ["0.6.0"], "detect-libc": ["2.0.4"], "rc": ["1.2.8"], "tar": ["6.2.1"], "typescript": ["5.8.3"], "ini": ["1.3.8"], "deep-extend": ["0.6.0"], "yallist": ["4.0.0"], "expand-template": ["2.0.3"], "napi-build-utils": ["2.0.0"], "fs-minipass": ["2.1.0"], "github-from-package": ["0.0.0"], "mkdirp-classic": ["0.5.3"], "node-gyp": ["8.4.1"], "chownr": ["2.0.0", "1.1.4"], "minipass": ["5.0.0", "3.3.6"], "node-abi": ["3.75.0"], "tar-fs": ["2.1.3"], "simple-get": ["4.0.1"], "pump": ["3.0.3"], "graceful-fs": ["4.2.11"], "which": ["2.0.2"], "minizlib": ["2.1.2"], "semver": ["7.7.2"], "isexe": ["2.0.0"], "nopt": ["5.0.0"], "make-fetch-happen": ["9.1.0"], "npmlog": ["6.0.2"], "env-paths": ["2.2.1"], "abbrev": ["1.1.1"], "end-of-stream": ["1.4.5"], "decompress-response": ["6.0.0"], "simple-concat": ["1.0.1"], "tar-stream": ["2.2.0"], "set-blocking": ["2.0.0"], "ssri": ["8.0.1"], "http-proxy-agent": ["4.0.1"], "agentkeepalive": ["4.6.0"], "lru-cache": ["6.0.0"], "https-proxy-agent": ["5.0.1"], "@tootallnate/once": ["1.1.2"], "bl": ["4.1.0"], "humanize-ms": ["1.2.1"], "agent-base": ["6.0.2"], "readable-stream": ["3.6.2"], "minipass-flush": ["1.0.5"], "console-control-strings": ["1.1.0"], "is-lambda": ["1.0.1"], "minipass-pipeline": ["1.2.4"], "minipass-fetch": ["1.4.1"], "promise-retry": ["2.0.1"], "minipass-collect": ["1.0.2"], "util-deprecate": ["1.0.2"], "string_decoder": ["1.3.0"], "socks-proxy-agent": ["6.2.1"], "http-cache-semantics": ["4.2.0"], "fs-constants": ["1.0.0"], "mimic-response": ["3.1.0"], "buffer": ["5.7.1"], "gauge": ["4.0.4"], "are-we-there-yet": ["3.0.1"], "cacache": ["15.3.0"], "retry": ["0.12.0"], "signal-exit": ["3.0.7"], "base64-js": ["1.5.1"], "strip-ansi": ["6.0.1"], "string-width": ["4.2.3"], "infer-owner": ["1.0.4"], "promise-inflight": ["1.0.1"], "unique-filename": ["1.1.1"], "ieee754": ["1.2.1"], "ansi-regex": ["5.0.1"], "is-fullwidth-code-point": ["3.0.0"], "emoji-regex": ["8.0.0"], "unique-slug": ["2.0.2"], "minipass-sized": ["1.0.3"], "encoding": ["0.1.13"], "err-code": ["2.0.3"], "imurmurhash": ["0.1.4"], "wide-align": ["1.1.5"], "p-map": ["4.0.0"], "color-support": ["1.1.3"], "aproba": ["2.1.0"], "@npmcli/move-file": ["1.1.2"], "has-unicode": ["2.0.1"], "delegates": ["1.0.0"], "@npmcli/fs": ["1.1.1"], "aggregate-error": ["3.1.0"], "socks": ["2.8.6"], "indent-string": ["4.0.0"], "smart-buffer": ["4.2.0"], "ip-address": ["9.0.5"], "clean-stack": ["2.2.0"], "@gar/promisify": ["1.1.3"], "sprintf-js": ["1.1.3"], "jsbn": ["1.1.0"]}